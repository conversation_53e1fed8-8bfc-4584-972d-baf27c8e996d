package com.howbuy.cgi.trade.simu.filter;

import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletException;
import java.io.IOException;
import java.io.PrintWriter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 系统异常监控过滤器集成测试
 * 
 * 这个测试不依赖外部静态方法，主要验证过滤器的基本功能
 *
 * <AUTHOR> Assistant
 * @date 2025-01-22
 */
class SystemExceptionMonitorIntegrationTest {

    @Test
    void testFilterBasicFunctionality() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置请求路径
        request.setRequestURI("/simu/test/nullpointer.htm");
        request.setMethod("GET");
        request.addParameter("testParam", "testValue");
        
        // 创建模拟的过滤器链
        MockFilterChain filterChain = new MockFilterChain() {
            @Override
            public void doFilter(javax.servlet.ServletRequest request, javax.servlet.ServletResponse response) 
                    throws IOException, ServletException {
                // 模拟写入系统错误响应
                PrintWriter writer = response.getWriter();
                writer.write("{\"code\":\"1999\",\"body\":null,\"desc\":\"系统错误，请联系好买\",\"timestampServer\":\"1667989593758\"}");
                writer.flush();
            }
        };
        
        // 执行过滤器（这里不会实际发送告警，但会执行过滤器逻辑）
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应内容被正确传递
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains("\"code\":\"1999\""));
        assertTrue(responseContent.contains("系统错误，请联系好买"));
    }
    
    @Test
    void testFilterWithNormalResponse() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置请求路径
        request.setRequestURI("/simu/test/normal.htm");
        request.setMethod("GET");
        
        // 创建模拟的过滤器链
        MockFilterChain filterChain = new MockFilterChain() {
            @Override
            public void doFilter(javax.servlet.ServletRequest request, javax.servlet.ServletResponse response) 
                    throws IOException, ServletException {
                // 模拟写入正常响应
                PrintWriter writer = response.getWriter();
                writer.write("{\"code\":\"0000\",\"body\":{\"data\":\"success\"},\"desc\":\"成功\"}");
                writer.flush();
            }
        };
        
        // 执行过滤器
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应内容被正确传递
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains("\"code\":\"0000\""));
        assertTrue(responseContent.contains("成功"));
    }
    
    @Test
    void testFilterWithNonSimuPath() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置非simu路径
        request.setRequestURI("/other/test.htm");
        request.setMethod("GET");
        
        // 创建模拟的过滤器链
        MockFilterChain filterChain = new MockFilterChain() {
            @Override
            public void doFilter(javax.servlet.ServletRequest request, javax.servlet.ServletResponse response) 
                    throws IOException, ServletException {
                // 模拟写入系统错误响应
                PrintWriter writer = response.getWriter();
                writer.write("{\"code\":\"1999\",\"body\":null,\"desc\":\"系统错误，请联系好买\"}");
                writer.flush();
            }
        };
        
        // 执行过滤器
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应内容被正确传递（即使包含错误码，但因为路径不匹配，不会被监控）
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains("\"code\":\"1999\""));
    }
    
    @Test
    void testFilterWithNonJsonResponse() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置请求路径
        request.setRequestURI("/simu/test/html.htm");
        request.setMethod("GET");
        
        // 创建模拟的过滤器链
        MockFilterChain filterChain = new MockFilterChain() {
            @Override
            public void doFilter(javax.servlet.ServletRequest request, javax.servlet.ServletResponse response) 
                    throws IOException, ServletException {
                // 模拟写入HTML响应
                PrintWriter writer = response.getWriter();
                writer.write("<html><body>Error Page</body></html>");
                writer.flush();
            }
        };
        
        // 执行过滤器
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应内容被正确传递
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains("<html>"));
        assertTrue(responseContent.contains("Error Page"));
    }
    
    @Test
    void testFilterWithEmptyResponse() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置请求路径
        request.setRequestURI("/simu/test/empty.htm");
        request.setMethod("GET");
        
        // 创建模拟的过滤器链（不写入任何内容）
        MockFilterChain filterChain = new MockFilterChain();
        
        // 执行过滤器
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应为空
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.isEmpty());
    }
    
    @Test
    void testFilterWithSensitiveParameters() throws ServletException, IOException {
        // 创建过滤器实例
        SystemExceptionMonitorFilter filter = new SystemExceptionMonitorFilter();
        
        // 创建请求和响应
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        
        // 设置请求路径和敏感参数
        request.setRequestURI("/simu/test/login.htm");
        request.setMethod("POST");
        request.addParameter("username", "testuser");
        request.addParameter("password", "secret123");
        request.addParameter("mobile", "13800138000");
        request.addParameter("normalParam", "normalValue");
        
        // 创建模拟的过滤器链
        MockFilterChain filterChain = new MockFilterChain() {
            @Override
            public void doFilter(javax.servlet.ServletRequest request, javax.servlet.ServletResponse response) 
                    throws IOException, ServletException {
                // 模拟写入系统错误响应
                PrintWriter writer = response.getWriter();
                writer.write("{\"code\":\"1999\",\"body\":null,\"desc\":\"系统错误，请联系好买\"}");
                writer.flush();
            }
        };
        
        // 执行过滤器
        assertDoesNotThrow(() -> {
            filter.doFilterInternal(request, response, filterChain);
        });
        
        // 验证响应内容被正确传递
        String responseContent = response.getContentAsString();
        assertTrue(responseContent.contains("\"code\":\"1999\""));
    }
}
