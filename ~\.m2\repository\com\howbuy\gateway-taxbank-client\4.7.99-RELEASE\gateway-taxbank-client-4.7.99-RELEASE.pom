<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.howbuy</groupId>
    <artifactId>gateway-taxbank-client</artifactId>
    <packaging>jar</packaging>
    <name>gateway-taxbank-client</name>
    <version>4.7.99-RELEASE</version>

    <properties>
        <java.version.client>1.8</java.version.client>
        <project.encoding>UTF-8</project.encoding>
        <com.howbuy.tms-common-client.version>4.7.99-RELEASE</com.howbuy.tms-common-client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>tms-common-client</artifactId>
            <version>${com.howbuy.tms-common-client.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>${java.version.client}</source>
                    <target>${java.version.client}</target>
                    <encoding>${project.encoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>