<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath />
	</parent>
	<groupId>com.howbuy.fbs</groupId>
	<artifactId>fbs-online-search</artifactId>
	<version>3.40.1-RELEASE</version>
	<name>fbs-online-search</name>
	<packaging>pom</packaging>
	<description>fbs-online-search</description>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<spring-cloud.version>Hoxton.SR12</spring-cloud.version>
		<spring-cloud-alibaba.version>2.2.8.RELEASE</spring-cloud-alibaba.version>
		<nacos.version>0.2.1.RELEASE</nacos.version>
		<mybatis.plus.version>3.4.2</mybatis.plus.version>
		<dubbo.version>2.7.15</dubbo.version>
		<zookeeper.version>3.4.13</zookeeper.version>
		<curator.version>4.2.0</curator.version>
		<druid.version>1.1.22</druid.version>
		<fastjson.version>1.2.72</fastjson.version>
		<hutool.version>5.4.1</hutool.version>
		<guava.version>16.0.1</guava.version>
		<fst.version>2.57</fst.version>
		<javassist.version>3.23.1-GA</javassist.version>
		<objenesis.version>2.6</objenesis.version>
		<log4j.version>1.2.17</log4j.version>
		<slf4j.version>1.7.30</slf4j.version>
		<ehcache.version>1.3.0</ehcache.version>
		<caffeine.version>2.9.0</caffeine.version>
		<jedis.version>2.9.3</jedis.version>
		<powermock.version>2.0.2</powermock.version>
		<com.howbuy.howbuy-message-service.version>2.3.2.1-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.fbs-common-facade.version>3.17.0-RELEASE</com.howbuy.fbs-common-facade.version>
		<com.howbuy.fbs-common-utils.version>3.7.0-RELEASE</com.howbuy.fbs-common-utils.version>
		<com.howbuy.hps-boot-starter.version>3.7.0-RELEASE</com.howbuy.hps-boot-starter.version>
		<com.howbuy.acc-center-facade.version>3.6.1-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.acc-common-utils.version>3.6.1-RELEASE</com.howbuy.acc-common-utils.version>
		<com.howbuy.fbs-online-search.version>3.40.1-RELEASE</com.howbuy.fbs-online-search.version>
		<com.howbuy.howbuy-auth-facade.version>2.1.9-RELEASE</com.howbuy.howbuy-auth-facade.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
		<com.howbuy.param-server-facade.version>3.40.1-RELEASE</com.howbuy.param-server-facade.version>
        <com.howbuy.param-center-new.version>3.40.1-RELEASE</com.howbuy.param-center-new.version>
    </properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.github.ben-manes.caffeine</groupId>
				<artifactId>caffeine</artifactId>
				<version>${caffeine.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-dependencies-bom</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-remoting-zookeeper</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-remoting-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis.plus.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<artifactId>druid</artifactId>
				<groupId>com.alibaba</groupId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.hutool</groupId>
				<artifactId>hutool-all</artifactId>
				<version>${hutool.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>de.ruedigermoeller</groupId>
				<artifactId>fst</artifactId>
				<version>${fst.version}</version>
			</dependency>
			<dependency>
				<artifactId>javassist</artifactId>
				<groupId>org.javassist</groupId>
				<version>${javassist.version}</version>
			</dependency>
			<dependency>
				<artifactId>objenesis</artifactId>
				<groupId>org.objenesis</groupId>
				<version>${objenesis.version}</version>
			</dependency>
			<dependency>
				<artifactId>log4j</artifactId>
				<groupId>log4j</groupId>
				<version>${log4j.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-module-testng</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-api-mockito2</artifactId>
				<version>${powermock.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>${nacos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fds</groupId>
				<artifactId>hps-boot-starter</artifactId>
				<version>${com.howbuy.hps-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-facade</artifactId>
				<version>${com.howbuy.fbs-online-search.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-service</artifactId>
				<version>${com.howbuy.fbs-online-search.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-online-search-dao</artifactId>
				<version>${com.howbuy.fbs-online-search.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-common-facade</artifactId>
				<version>${com.howbuy.fbs-common-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy.pa.framework</groupId>
						<artifactId>howbuy-framework-rpc</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.fbs</groupId>
				<artifactId>fbs-common-utils</artifactId>
				<version>${com.howbuy.fbs-common-utils.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>howbuy-cache-client-trade</artifactId>
						<groupId>com.howbuy.pa.cache</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.acc</groupId>
				<artifactId>acc-common-utils</artifactId>
				<version>${com.howbuy.acc-common-utils.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>spring-context</artifactId>
						<groupId>org.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>acc-common-cache</artifactId>
						<groupId>com.howbuy.acc</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<artifactId>acc-center-facade</artifactId>
				<groupId>com.howbuy.acccenter</groupId>
				<version>${com.howbuy.acc-center-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba</groupId>
						<artifactId>fastjson</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.acc</groupId>
						<artifactId>acc-common-cache</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.empire-db</groupId>
						<artifactId>empire-db</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.howbuy.pa.cache</groupId>
						<artifactId>howbuy-cache-client-trade</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-jdbc</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-jms</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-test</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.mybatis</groupId>
						<artifactId>mybatis-spring</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.mybatis</groupId>
						<artifactId>mybatis</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.github.pagehelper</groupId>
						<artifactId>pagehelper</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework.security</groupId>
						<artifactId>spring-security-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>junit</groupId>
						<artifactId>junit</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.qunji</groupId>
						<artifactId>qunjicommon</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.qunji</groupId>
						<artifactId>qunjisecure</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.qunji</groupId>
						<artifactId>qunjiwebserviceclient</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.httpcomponents</groupId>
						<artifactId>httpclient</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.httpcomponents</groupId>
						<artifactId>httpcore</artifactId>
					</exclusion>
					<exclusion>
						<groupId>dom4j</groupId>
						<artifactId>dom4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-auth-facade</artifactId>
				<version>${com.howbuy.howbuy-auth-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>6.0.1-RELEASE</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-cachemanagement</artifactId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
				<version>${ehcache.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-server-facade</artifactId>
				<version>${com.howbuy.param-center-new.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.pdc</groupId>
				<artifactId>pdc-online-facade</artifactId>
				<version>${com.howbuy.pdc-online-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy.common</groupId>
						<artifactId>common-facade</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>nexus-release</id>
			<name>Nexus Releases Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>nexus-snapshots</id>
			<name>Nexus Snapshots Repository</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

</project>