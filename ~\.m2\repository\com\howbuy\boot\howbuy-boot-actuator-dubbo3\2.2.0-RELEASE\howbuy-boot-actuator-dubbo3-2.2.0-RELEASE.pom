<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.howbuy.boot</groupId>
        <artifactId>howbuy-boot-actuator-parent</artifactId>
        <version>2.2.0-RELEASE</version>
    </parent>

    <artifactId>howbuy-boot-actuator-dubbo3</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <dubbo.version>3.2.3</dubbo.version>
    <com.howbuy.howbuy-boot-actuator-parent.version>2.2.0-RELEASE</com.howbuy.howbuy-boot-actuator-parent.version>
</properties>
    <dependencies>
        <!-- 引入 Spring Boot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 引入 Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-ccms-watcher</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cachemanagement</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.howbuy.boot</groupId>
            <artifactId>howbuy-boot-actuator-core</artifactId>
            <version>${com.howbuy.howbuy-boot-actuator-parent.version}</version>
        </dependency>
        <dependency>
            <artifactId>lombok</artifactId>
            <groupId>org.projectlombok</groupId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>