<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.howbuy.payonline</groupId>
	<artifactId>pay-online</artifactId>
	<version>********-RELEASE</version>
	<packaging>pom</packaging>
	<name>pay-online</name>

	
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath />
	</parent>

	<properties>
		
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		
		<spring.version>5.2.15.RELEASE</spring.version>
		<nacos-config-spring-boot-starter.version>0.2.10</nacos-config-spring-boot-starter.version>
		<spring-boot-admin-starter-client.version>2.3.1</spring-boot-admin-starter-client.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>
		<aspectj.version>1.6.8</aspectj.version>
		<pay-online.version>1.0.0-SNAPSHOT</pay-online.version>
		<sitemesh.version>2.4.2</sitemesh.version>
		<scheduler.version>0.0.3-SNAPSHOT</scheduler.version>
		<druid.version>0.2.9</druid.version>
		<cglib.version>2.2.0</cglib.version>
		<mybatis.version>3.5.5</mybatis.version>
		<mysql.version>5.1.14</mysql.version>
		<activemq.version>5.7.0</activemq.version>
		<xson.version>1.0.1</xson.version>
		<fastjson.version>1.1.41</fastjson.version>
		<dubbo.version>2.7.15</dubbo.version>
		<ojdbc6.version>11.2.0.2.0</ojdbc6.version>
		<zkclient.version>0.4</zkclient.version>
		<hessian.version>4.0.7</hessian.version>
		<pagehelper.version>4.1.6</pagehelper.version>
		<atomikos.version>3.9.3</atomikos.version>
		<javax.servlet.version>2.4</javax.servlet.version>
		<jstl.version>1.2</jstl.version>
		<taglibs.version>1.1.2</taglibs.version>
		<javax.servlet.jsp.version>2.0</javax.servlet.jsp.version>
		<javax.el.version>2.2</javax.el.version>
		<org.mybatis.version>2.0.5</org.mybatis.version>
		<org.apache.empire-db.version>2.4.2</org.apache.empire-db.version>
		<javax.validation.version>1.1.0.Final</javax.validation.version>
		<org.hibernate.version>5.3.5.Final</org.hibernate.version>
		<ch.qos.logback.version>1.2.3</ch.qos.logback.version>
		<commons.pool.version>1.6</commons.pool.version>
		<commons.beanutils.version>1.9.2</commons.beanutils.version>
		<commons.io.version>2.15.1</commons.io.version>
		<com.howbuy.notifyUtil.version>1.0.0</com.howbuy.notifyUtil.version>
		<com.howbuy.fps.version>1.0.0-SNAPSHOT</com.howbuy.fps.version>
		<junit.version>4.9</junit.version>
		<powermock.version>2.0.2</powermock.version>
		<com.howbuy.fds.redis.version>1.0.0-SNAPSHOT</com.howbuy.fds.redis.version>
		<com.howbuy.common.version>1.0.0-SNAPSHOT</com.howbuy.common.version>
		<commons-fileupload.version>1.2</commons-fileupload.version>
		<org.codehaus.jackson.version>1.9.13</org.codehaus.jackson.version>
		<commons.lang.version>2.4</commons.lang.version>
		<lombok.version>1.16.22</lombok.version>
		<org.mapstruct.version>1.4.1.Final</org.mapstruct.version>
		<c3p0.version>0.9.1.2</c3p0.version>
		<basic.version>1.0.0-SNAPSHOT</basic.version>
		<banknew.version>1.1.8-RELEASE</banknew.version>
		<cluster.version>0.86</cluster.version>
		<nacos-spring-context.version>1.1.0</nacos-spring-context.version>
		<jackson-databind.version>2.11.4</jackson-databind.version>
		<jackson-core.version>2.11.4</jackson-core.version>
		<gson.version>2.8.5</gson.version>
		<org.javassist.version>3.20.0-GA</org.javassist.version>
		
		<ehcache.version>1.3.0</ehcache.version>
		<jedis.version>2.9.3</jedis.version>
		<commons-pool2.version>2.4.3</commons-pool2.version>
		
		<com.howbuy.howbuy-auth-facade.version>2.1.8-RELEASE</com.howbuy.howbuy-auth-facade.version>
        <com.howbuy.banknew.version>********-RELEASE</com.howbuy.banknew.version>
        <com.howbuy.pay-common.version>2.5.5-RELEASE</com.howbuy.pay-common.version>
	    <com.howbuy.pay-online.version>********-RELEASE</com.howbuy.pay-online.version>
        <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
        <com.howbuy.common-facade.version>3.5.7-RELEASE</com.howbuy.common-facade.version>
        <com.howbuy.common-config.version>3.5.7-RELEASE</com.howbuy.common-config.version>
        <com.howbuy.message-public-client.version>5.1.12-RELEASE</com.howbuy.message-public-client.version>
        <com.howbuy.fin-online-facade.version>3.2.8-RELEASE</com.howbuy.fin-online-facade.version>
        <com.howbuy.param-server-facade.version>********-RELEASE</com.howbuy.param-server-facade.version>
        <com.howbuy.acc-center-facade.version>3.6.0-RELEASE</com.howbuy.acc-center-facade.version>
		<com.howbuy.pension-order-client.version>4.7.34-RELEASE</com.howbuy.pension-order-client.version>
		<com.howbuy.howbuy-dfile.version>1.18.1-RELEASE</com.howbuy.howbuy-dfile.version>
</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>de.codecentric</groupId>
				<artifactId>spring-boot-admin-starter-client</artifactId>
				<version>${spring-boot-admin-starter-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.boot</groupId>
				<artifactId>nacos-config-spring-boot-starter</artifactId>
				<version>${nacos-config-spring-boot-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
			</dependency>

			
			<dependency>
				<groupId>org.softamis</groupId>
				<artifactId>cluster4spring</artifactId>
				<version>${cluster.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>notifyUtil</artifactId>
				<version>${com.howbuy.notifyUtil.version}</version>
			</dependency>
			
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>${junit.version}</version>
			</dependency>
			
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>servlet-api</artifactId>
				<version>${javax.servlet.version}</version>
				<scope>provided</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-test</artifactId>
				<version>${spring.version}</version>
			</dependency>
			
			<dependency>
				<groupId>jstl</groupId>
				<artifactId>jstl</artifactId>
				<version>${jstl.version}</version>
			</dependency>
			<dependency>
				<groupId>taglibs</groupId>
				<artifactId>standard</artifactId>
				<version>${taglibs.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet.jsp</groupId>
				<artifactId>jsp-api</artifactId>
				<version>${javax.servlet.jsp.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>javax.el</groupId>
				<artifactId>el-api</artifactId>
				<version>${javax.el.version}</version>
				<scope>provided</scope>
			</dependency>
			
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-context-support</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-tx</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jdbc</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jms</artifactId>
				<version>${spring.version}</version>
			</dependency>
			<dependency>
				<groupId>net.sourceforge.cglib</groupId>
				<artifactId>com.springsource.net.sf.cglib</artifactId>
				<version>${cglib.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjweaver</artifactId>
				<version>${aspectj.version}</version>
			</dependency>
			<dependency>
				<groupId>org.aspectj</groupId>
				<artifactId>aspectjrt</artifactId>
				<version>${aspectj.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>atomikos-util</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jdbc</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-common</artifactId>
				<version>${com.howbuy.pay-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-facade</artifactId>
				<version>${com.howbuy.pay-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-service</artifactId>
				<version>${com.howbuy.pay-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.payonline</groupId>
				<artifactId>pay-online-dao</artifactId>
				<version>${com.howbuy.pay-online.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-service</artifactId>
				<version>${com.howbuy.common-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-facade</artifactId>
				<version>${com.howbuy.common-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.common</groupId>
				<artifactId>common-config</artifactId>
				<version>${com.howbuy.common-config.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>param-server-facade</artifactId>
				<version>${com.howbuy.param-server-facade.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.acccenter</groupId>
				<artifactId>acc-center-facade</artifactId>
				<version>${com.howbuy.acc-center-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.howbuy.pa.cache</groupId>
						<artifactId>howbuy-cache-client-trade</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.bank</groupId>
				<artifactId>banknew</artifactId>
				<version>${com.howbuy.banknew.version}</version> 
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-nfs</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.dfile</groupId>
				<artifactId>howbuy-dfile-impl-webdav</artifactId>
				<version>${com.howbuy.howbuy-dfile.version}</version>
			</dependency>
			
			<dependency>
				 <groupId>org.apache.activemq</groupId>
				 <artifactId>activemq-core</artifactId>
				 <version>${activemq.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.druid</groupId>
				<artifactId>druid-wrapper</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis</artifactId>
				<version>${mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>mysql</groupId>
				<artifactId>mysql-connector-java</artifactId>
				<version>${mysql.version}</version>
			</dependency>
			<dependency>
				<groupId>com.caucho</groupId>
				<artifactId>hessian</artifactId>
				<version>${hessian.version}</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis</groupId>
				<artifactId>mybatis-spring</artifactId>
				<version>${org.mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.empire-db</groupId>
				<artifactId>empire-db</artifactId>
				<version>${org.apache.empire-db.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>opensymphony</groupId>
				<artifactId>sitemesh</artifactId>
				<version>${sitemesh.version}</version>
			</dependency>
			<dependency>
				<groupId>javax.validation</groupId>
				<artifactId>validation-api</artifactId>
				<version>${javax.validation.version}</version>
			</dependency>
			<dependency>
				<groupId>org.hibernate</groupId>
				<artifactId>hibernate-validator</artifactId>
				<version>${org.hibernate.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<version>${ch.qos.logback.version}</version>
			</dependency>
			<dependency> 
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons.beanutils.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons.io.version}</version>
			</dependency>

			<dependency>
				<groupId>commons-fileupload</groupId>
				<artifactId>commons-fileupload</artifactId>
				<version>${commons-fileupload.version}</version>
			</dependency>
			<dependency>
				<groupId>org.codehaus.jackson</groupId>
				<artifactId>jackson-mapper-asl</artifactId>
				<version>${org.codehaus.jackson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.projectlombok</groupId>
				<artifactId>lombok</artifactId>  
				<version>${lombok.version}</version>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>com.howbuy.cc.message</groupId>
				<artifactId>message-public-client</artifactId>
				<version>${com.howbuy.message-public-client.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.finonline</groupId>
				<artifactId>fin-online-facade</artifactId>
				<version>${com.howbuy.fin-online-facade.version}</version>
				<exclusions>
					<exclusion>
						<groupId>*</groupId>
						<artifactId>*</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-auth-facade</artifactId>
				<version>${com.howbuy.howbuy-auth-facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>pay-common-model</artifactId>
				<version>${com.howbuy.pay-common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>pay-common-utils</artifactId>
				<version>${com.howbuy.pay-common.version}</version>
			</dependency>

			<dependency>
				<groupId>com.oracle</groupId>
				<artifactId>ojdbc6</artifactId>
				<version>${ojdbc6.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-remoting-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.dubbo</groupId>
						<artifactId>dubbo-remoting-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.dubbo</groupId>
						<artifactId>dubbo-common</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.101tec</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
				
				<exclusions>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.mapstruct</groupId>
				<artifactId>mapstruct</artifactId>
				<version>${org.mapstruct.version}</version>
			</dependency>
			<dependency>
				<groupId>c3p0</groupId>
				<artifactId>c3p0</artifactId>
				<version>${c3p0.version}</version>
			</dependency>

			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>utils</artifactId>
				<version>1.0.0-SNAPSHOT</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-spring-context</artifactId>
				<version>${nacos-spring-context.version}</version>
				<exclusions>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-databind</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.fasterxml.jackson.core</groupId>
						<artifactId>jackson-core</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>commons-logging</artifactId>
						<groupId>commons-logging</groupId>
					</exclusion>
					<exclusion>
						<artifactId>commons-io</artifactId>
						<groupId>commons-io</groupId>
					</exclusion>
					<exclusion>
						<artifactId>commons-codec</artifactId>
						<groupId>commons-codec</groupId>
					</exclusion>
					<exclusion>
						<groupId>com.google.guava</groupId>
						<artifactId>guava</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-databind</artifactId>
				<version>${jackson-databind.version}</version>
			</dependency>
			<dependency>
				<groupId>com.fasterxml.jackson.core</groupId>
				<artifactId>jackson-core</artifactId>
				<version>${jackson-core.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>2.2.1-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-amq</artifactId>
				<version>2.2.1-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>2.2.1-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>6.0.1-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.google.code.gson</groupId>
				<artifactId>gson</artifactId>
				<version>${gson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.javassist</groupId>
				<artifactId>javassist</artifactId>
				<version>${org.javassist.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>pension-order-client</artifactId>
				<version>${com.howbuy.pension-order-client.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.hibernate.validator</groupId>
						<artifactId>hibernate-validator</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			
			<dependency>
				<groupId>org.mockito</groupId>
				<artifactId>mockito-core</artifactId>
				<version>3.3.3</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-module-testng</artifactId>
				<version>${powermock.version}</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>org.powermock</groupId>
				<artifactId>powermock-api-mockito2</artifactId>
				<version>${powermock.version}</version>
				<scope>test</scope>
			</dependency>

			
			<dependency>
				<groupId>net.sf.ehcache</groupId>
				<artifactId>ehcache</artifactId>
				<version>${ehcache.version}</version>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>${jedis.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-pool2</artifactId>
				<version>${commons-pool2.version}</version>
			</dependency>

		</dependencies>
	</dependencyManagement>
	
	<distributionManagement>
		<repository>
			<id>howbuy-release</id>
			<name>howbuy-release</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-release/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshot</id>
			<name>howbuy-snapshot</name>
			<url>http://nx-java.howbuy.pa/repository/howbuy-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>${org.mapstruct.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<encoding>${project.build.sourceEncoding}</encoding>
				</configuration>
				<executions>
					<execution>
						<id>attach-source</id>
						<phase>install</phase>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
	</project>