//package com.howbuy.cgi.trade.simu.config;
//
//import com.howbuy.cgi.trade.simu.filter.SystemExceptionMonitorFilter;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * 系统异常监控配置
// *
// * <AUTHOR> Assistant
// * @date 2025-01-22
// */
//@Configuration
//public class SystemExceptionMonitorConfig {
//
//    /**
//     * 注册系统异常监控过滤器
//     */
//    @Bean
//    public FilterRegistrationBean<SystemExceptionMonitorFilter> systemExceptionMonitorFilter() {
//        FilterRegistrationBean<SystemExceptionMonitorFilter> registration = new FilterRegistrationBean<>();
//        registration.setFilter(new SystemExceptionMonitorFilter());
//        registration.addUrlPatterns("/simu/*");
//        registration.setName("systemExceptionMonitorFilter");
//        registration.setOrder(1); // 设置较高优先级
//        return registration;
//    }
//}
