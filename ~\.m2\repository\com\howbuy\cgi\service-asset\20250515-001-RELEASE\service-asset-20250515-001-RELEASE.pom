<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-service</artifactId>
		<version>20250515-001-RELEASE</version>
	</parent>

	<name>service-asset</name>
	<artifactId>service-asset</artifactId>
	<version>20250515-001-RELEASE</version>

	<dependencies>

		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>service-common</artifactId>
		</dependency>

		<!-- howbuy uitl -->
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>utils</artifactId>
		</dependency>

		<!-- spring framework -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>
		
		<dependency>
			<groupId>commons-pool</groupId>
			<artifactId>commons-pool</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

	</dependencies>

</project>