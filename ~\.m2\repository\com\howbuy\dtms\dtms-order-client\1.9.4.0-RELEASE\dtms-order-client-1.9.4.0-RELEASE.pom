<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dtms-order</artifactId>
        <groupId>com.howbuy.dtms</groupId>
        <version>1.9.4.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>dtms-order-client</name>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.26</version>
        </dependency>
        <dependency>
            <artifactId>howbuy-commons-validator</artifactId>
            <groupId>com.howbuy.commons.validator</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.dtms</groupId>
            <artifactId>dtms-common-enums</artifactId>
            <version>${com.howbuy.dtms-common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
    </dependencies>
    <artifactId>dtms-order-client</artifactId>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    <com.howbuy.dtms-common.version>1.9.4.0-RELEASE</com.howbuy.dtms-common.version>
</properties>


</project>