<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.howbuy.hkacconline</groupId>
        <artifactId>hk-acc-online</artifactId>
        <version>3.6.5-RELEASE</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>hk-acc-online-facade</artifactId>
    <packaging>jar</packaging>
    <name>hk-acc-online-facade</name>

    <dependencies>
        <dependency>
            <groupId>com.howbuy.common</groupId>
            <artifactId>common-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy.common</groupId>
            <artifactId>common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy.commons.validator</groupId>
            <artifactId>howbuy-commons-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- 源码 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>