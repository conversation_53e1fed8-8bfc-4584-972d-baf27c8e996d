<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.howbuy.crm</groupId>
    <artifactId>crm-td-client</artifactId>
    <version>1.9.4.0-RELEASE</version>
    <packaging>jar</packaging>

    <name>crm-td-client</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.7</maven.compiler.source>
        <maven.compiler.target>1.7</maven.compiler.target>
        <com.howbuy.base-commons.version>1.8.2.2-RELEASE</com.howbuy.base-commons.version>
        <com.github.pagehelper.version>5.1.9</com.github.pagehelper.version>
        <joda.time.version>2.10.2</joda.time.version>
    </properties>

    <repositories>
        <repository>
            <id>nexus_howbuy</id>
            <name>nexus_howbuy</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/com.howbuy.cc/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/com.howbuy.cc.snapshot</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <!--生成 APIDOC 用-->
        <!--<dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>4.3.5.RELEASE</version>
            <scope>compile</scope>
        </dependency>-->
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>base-commons</artifactId>
            <version>${com.howbuy.base-commons.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${com.github.pagehelper.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.30</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.9.9</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>