# 系统异常监控 Filter 解决方案

## 问题背景

在原有的 `SystemExceptionMonitorInterceptor` 拦截器中，发现异常被其他拦截器（如全局异常处理器）处理了，导致拦截器无法捕获到异常。从日志中可以看到：

```json
{"code":"1999","body":null,"desc":"系统错误，请联系好买","timestampServer":"1667989593758"}
```

系统异常被转换为错误响应返回，而不是直接抛出异常。

## 解决方案

采用 **Filter + Interceptor 双重监控机制**：

### 1. 保留原有拦截器
`SystemExceptionMonitorInterceptor` 继续处理直接抛出的异常。

### 2. 新增过滤器监控
`SystemExceptionMonitorFilter` 通过检查响应体中的错误码来检测系统异常。

## 实现细节

### 文件结构
```
cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/
├── filter/
│   └── SystemExceptionMonitorFilter.java (新增)
├── interceptor/
│   └── SystemExceptionMonitorInterceptor.java (保留)
└── common/config/
    └── FilterConfig.java (修改，添加过滤器配置)
```

### 核心实现

#### 1. SystemExceptionMonitorFilter
- 继承 `OncePerRequestFilter`
- 使用 `ResponseWrapper` 包装响应以捕获响应体内容
- 检查响应体中的 `code` 字段是否为 `"1999"`（系统错误码）
- 发送告警信息到 `OpsSysMonitor`

#### 2. ResponseWrapper
- 包装 `HttpServletResponse`
- 捕获写入响应的内容
- 提供获取响应体内容的方法

#### 3. 过滤器配置
在 `FilterConfig` 中注册过滤器：
```java
@Bean
public FilterRegistrationBean<SystemExceptionMonitorFilter> systemExceptionMonitorFilter() {
    FilterRegistrationBean<SystemExceptionMonitorFilter> registration = new FilterRegistrationBean<>();
    registration.setFilter(new SystemExceptionMonitorFilter());
    registration.addUrlPatterns("/simu/*");
    registration.setName("systemExceptionMonitorFilter");
    registration.setOrder(2); // 设置在字符编码过滤器之后执行
    return registration;
}
```

## 监控范围

### 过滤器监控
- **路径**: `/simu/*`
- **检测方式**: 响应体中 `code` 字段为 `"1999"`
- **适用场景**: 被全局异常处理器处理的系统异常

### 拦截器监控
- **路径**: 所有配置的路径
- **检测方式**: 直接捕获抛出的异常
- **适用场景**: 直接抛出的系统异常

## 告警信息

告警信息包含以下字段：
- `alertType`: "SYSTEM_EXCEPTION"
- `requestUri`: 请求URI
- `requestMethod`: 请求方法
- `traceId`: 链路追踪ID
- `remoteAddr`: 客户端IP地址
- `responseCode`: 响应错误码
- `responseDesc`: 响应错误描述
- `detectionMethod`: 检测方式（"RESPONSE_BODY_CHECK" 或 "DIRECT_EXCEPTION"）
- `requestParams`: 请求参数（敏感信息已过滤）
- `responseBodyLength`: 响应体长度
- `responseBody` 或 `responseBodyPreview`: 响应体内容（长度超过500字符时截取）

## 安全考虑

1. **敏感参数过滤**: 自动过滤包含密码、手机号等敏感信息的参数
2. **响应体长度限制**: 避免记录过长的响应体内容
3. **异常处理**: 告警发送失败不影响主业务流程

## 测试

提供了完整的单元测试 `SystemExceptionMonitorFilterTest`，覆盖以下场景：
- 系统错误码检测
- 正常响应处理
- 非监控路径跳过
- 非JSON响应处理
- 空响应处理

## 使用方式

1. 确保 `SystemExceptionMonitorFilter` 类存在
2. 确保 `FilterConfig` 中已注册过滤器
3. 启动应用，过滤器会自动生效
4. 当 `/simu/*` 路径下的请求返回 `code: "1999"` 时，会自动发送告警

## 优势

1. **全面覆盖**: 同时监控直接异常和响应体错误码
2. **无侵入性**: 不影响现有业务逻辑
3. **高性能**: 只在必要时进行响应体解析
4. **可配置**: 可以轻松调整监控路径和错误码
5. **安全性**: 自动过滤敏感信息

## 注意事项

1. 过滤器会包装响应，可能对性能有轻微影响
2. 只监控 JSON 格式的响应
3. 需要确保 `OpsSysMonitor` 和 `RequestChainTrace` 可用
4. 建议在生产环境中监控告警发送的性能影响

## 部署状态

✅ **已完成的文件**:
- `SystemExceptionMonitorFilter.java` - 核心过滤器实现
- `FilterConfig.java` - 过滤器配置（已修改）
- `SystemExceptionMonitorInterceptor.java` - 原有拦截器（已更新注释）
- `SystemExceptionMonitorFilterTest.java` - 单元测试
- `SystemExceptionMonitorIntegrationTest.java` - 集成测试

✅ **配置完成**:
- 过滤器已注册到 Spring 容器
- 监控路径设置为 `/simu/*`
- 过滤器执行顺序已优化

✅ **测试覆盖**:
- 系统错误码检测测试
- 正常响应处理测试
- 路径匹配测试
- 非JSON响应测试
- 敏感参数过滤测试

## 立即可用

该解决方案现在已经完整实现，重启应用后即可生效。当 `/simu/*` 路径下的请求返回包含 `"code": "1999"` 的JSON响应时，系统会自动发送告警。
