<?xml version="1.0" encoding="utf-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.howbuy</groupId>
  <artifactId>ftx-common</artifactId>
  <version>2.1.0-RELEASE</version>
  <packaging>pom</packaging>
  <name>ftx-common</name>
  <description>订单联机公共工程</description>
  <developers>
    <developer>
      <id>leslie</id>
      <name>李超</name>
      <email><EMAIL></email>
      <timezone>8</timezone>
    </developer>
  </developers>
  <issueManagement>
    <system>JIRA</system>
    <url>http://ims.intelnal.howbuy.com/</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>nexus-release</id>
      <name>Nexus Releases Repository</name>
      <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
    </repository>
    <snapshotRepository>
      <id>nexus-snapshots</id>
      <name>Nexus Snapshots Repository</name>
      <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
  <properties>
    <spring.version>4.1.0.RELEASE</spring.version>
    <commons.lang.version>2.4</commons.lang.version>
    <lombok.version>1.18.20</lombok.version>
    <zookeeper.version>3.4.6</zookeeper.version>
    <com.howbuy.howbuy-trace.version>1.0.4-RELEASE</com.howbuy.howbuy-trace.version>
    <com.howbuy.common-service.version>3.5.7-RELEASE</com.howbuy.common-service.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <dubbo.version>2.7.15</dubbo.version>
    <fastjson.version>1.1.41</fastjson.version>
    <com.howbuy.ftx-common.version>2.1.0-RELEASE</com.howbuy.ftx-common.version>
    <junit.version>4.12</junit.version>
    <org.slf4j.version>1.7.4</org.slf4j.version>
    <java.version>1.8</java.version>
    <com.howbuy.acc-common-utils.version>3.5.13-RELEASE</com.howbuy.acc-common-utils.version>
    <org.jodd.version>3.5.2</org.jodd.version>
    <pagehelper.version>3.6.3</pagehelper.version>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <org.springframework.security.version>4.1.0.RELEASE</org.springframework.security.version>
    <nacos.version>1.0.0</nacos.version>
    <com.howbuy.howbuy-sharding-id.version>2.0.4-RELEASE</com.howbuy.howbuy-sharding-id.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.shardingsphere</groupId>
        <artifactId>shardingsphere-jdbc-core</artifactId>
        <version>5.4.1</version>
        <exclusions>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.dubbo</groupId>
        <artifactId>dubbo</artifactId>
        <version>${dubbo.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.howbuy.common</groupId>
        <artifactId>common-service</artifactId>
        <version>${com.howbuy.common-service.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.howbuy.pa.cache</groupId>
            <artifactId>howbuy-cache-client-trade</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${org.slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.zookeeper</groupId>
        <artifactId>zookeeper</artifactId>
        <version>${zookeeper.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.sun.jmx</groupId>
            <artifactId>jmxri</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.sun.jdmk</groupId>
            <artifactId>jmxtools</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.jms</groupId>
            <artifactId>jms</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-core</artifactId>
        <version>${org.springframework.security.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${spring.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>${spring.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jodd</groupId>
        <artifactId>jodd-vtor</artifactId>
        <version>${org.jodd.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.pagehelper</groupId>
        <artifactId>pagehelper</artifactId>
        <version>${pagehelper.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.4</version>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>ftx-common-model</artifactId>
        <version>${com.howbuy.ftx-common.version}</version>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>ftx-common-utils</artifactId>
        <version>${com.howbuy.ftx-common.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.0.19</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>2.5.14.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.0.1</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-spring-context</artifactId>
        <version>${nacos.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.howbuy.acc</groupId>
        <artifactId>acc-common-utils</artifactId>
        <version>${com.howbuy.acc-common-utils.version}</version>
      </dependency>
      <dependency>
        <groupId>com.howbuy.sharding</groupId>
        <artifactId>howbuy-sharding-id</artifactId>
        <version>${com.howbuy.howbuy-sharding-id.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.howbuy</groupId>
        <artifactId>howbuy-trace</artifactId>
        <version>${com.howbuy.howbuy-trace.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>public</id>
      <name>public respository</name>
      <url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>public-plugin</id>
      <name>public plugin respository</name>
      <url>http://mvn.intelnal.howbuy.com/nexus/content/groups/public/</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <defaultGoal>install</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>1.1.0</version>
          <executions>
            <execution>
              <id>flatten</id>
              <phase>process-resources</phase>
              <goals>
                <goal>flatten</goal>
              </goals>
            </execution>
            <execution>
              <id>flatten.clean</id>
              <phase>clean</phase>
              <goals>
                <goal>clean</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <updatePomFile>true</updatePomFile>
            <flattenMode>resolveCiFriendliesOnly</flattenMode>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.2</version>
          <configuration>
            <source>${java.version}</source>
            <target>${java.version}</target>
            <optimise>true</optimise>
            <debug>true</debug>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.4</version>
          <executions>
            <execution>
              <id>attach-source</id>
              <phase>install</phase>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-war-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.5.3</version>
          <executions>
            <execution>
              <id>make-assembly</id>
              <phase>package</phase>
              <goals>
                <goal>single</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <appendAssemblyId>true</appendAssemblyId>
            <descriptors>
              <descriptor>assembly/package.xml</descriptor>
            </descriptors>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.18</version>
          <configuration>
            <skipTests>true</skipTests>
            <argLine>-Dfile.encoding=${project.build.sourceEncoding}</argLine>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>2.18</version>
          <configuration>
            <aggregate>true</aggregate>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>cobertura-maven-plugin</artifactId>
          <version>2.6</version>
          <executions>
            <execution>
              <phase>test</phase>
              <goals>
                <goal>cobertura</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <aggregate>true</aggregate>
            <encoding>${project.build.sourceEncoding}</encoding>
            <formats>
              <format>html</format>
              <format>xml</format>
            </formats>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>emma-maven-plugin</artifactId>
          <version>1.0-alpha-3</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>2.4</version>
          <executions>
            <execution>
              <phase>validate</phase>
              <goals>
                <goal>taglist</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <aggregate>true</aggregate>
            <tags>
              <tag>TODO</tag>
              <tag>@deprecated</tag>
            </tags>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>2.13</version>
          <executions>
            <execution>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <configLocation>../howbuy-checkstyle.xml</configLocation>
            <includeResources>false</includeResources>
            <failOnViolation>true</failOnViolation>
            <violationSeverity>info</violationSeverity>
            <maxAllowedViolations>0</maxAllowedViolations>
            <consoleOutput>true</consoleOutput>
            <encoding>${project.build.sourceEncoding}</encoding>
          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-pmd-plugin</artifactId>
          <version>2.6</version>
          <executions>
            <execution>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <aggregate>true</aggregate>
            <failurePriority>5</failurePriority>
            <failOnViolation>true</failOnViolation>
            <targetJdk>1.6</targetJdk>
            <verbose>true</verbose>
            <outputEncoding>${project.build.sourceEncoding}</outputEncoding>
            <rulesets>
              <rulesets>../howbuy-pmd.xml</rulesets>
            </rulesets>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>findbugs-maven-plugin</artifactId>
          <version>2.5.5</version>
          <executions>
            <execution>
              <phase>validate</phase>
              <goals>
                <goal>check</goal>
              </goals>
            </execution>
          </executions>
          <configuration>
            <effort>Max</effort>
            <threshold>Low</threshold>
            <xmlOutput>true</xmlOutput>
            <failOnError>true</failOnError>
            <outputEncoding>${project.build.sourceEncoding}</outputEncoding>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>jdepend-maven-plugin</artifactId>
          <version>2.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>2.5</version>
          <configuration>
            <aggregate>true</aggregate>
            <linkJavadoc>true</linkJavadoc>
            <javadocDir>${project.reporting.outputDirectory}/apidocs</javadocDir>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>javancss-maven-plugin</artifactId>
          <version>2.1</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>dashboard-maven-plugin</artifactId>
          <version>1.0.0-beta-1</version>
        </plugin>
        <plugin>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>2.7</version>
        </plugin>
        <plugin>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.4</version>
          <configuration>
            <locales>UTF-8</locales>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <outputDirectory>target/site</outputDirectory>
    <plugins>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.1</version>
        <configuration>
          <encoding>${project.reporting.outputEncoding}</encoding>
          <aggregate>true</aggregate>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <tags>
            <tag>TODO</tag>
            <tag>@deprecated</tag>
          </tags>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.13</version>
      </plugin>
      <plugin>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>2.6</version>
        <reportSets>
          <reportSet>
            <reports>
              <report>pmd</report>
              <report>cpd</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
        <version>2.5.5</version>
        <configuration>
          <effort>Max</effort>
          <threshold>Low</threshold>
          <xmlOutput>true</xmlOutput>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>cobertura-maven-plugin</artifactId>
        <version>2.6</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
        <version>2.5</version>
        <reportSets>
          <reportSet>
            <id>aggregate</id>
            <reports>
              <report>aggregate</report>
              <report>test-aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javancss-maven-plugin</artifactId>
        <version>2.1</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>jdepend-maven-plugin</artifactId>
        <version>2.0</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>dashboard-maven-plugin</artifactId>
        <version>1.0.0-beta-1</version>
      </plugin>
    </plugins>
  </reporting>
</project>
