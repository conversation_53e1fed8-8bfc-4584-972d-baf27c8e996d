<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.12.RELEASE</version>
		<relativePath /> 
	</parent>

	<groupId>com.howbuy.tms</groupId>
	<artifactId>robot-order-center</artifactId>
	<version>4.7.99-RELEASE</version>
	<packaging>pom</packaging>
	<name>robot-order-center</name>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<dubbo.version>2.7.15</dubbo.version>
		<spring-cloud.version>Hoxton.SR12</spring-cloud.version>
		<spring-boot.version>2.3.12.RELEASE</spring-boot.version>
		<mybatis-spring-boot-starter.version>2.2.0</mybatis-spring-boot-starter.version>
		<zookeeper.version>3.4.13</zookeeper.version>
		<druid.version>1.2.8</druid.version>
		<pagehelper.version>4.1.4</pagehelper.version>
		<fastjson.version>1.2.17</fastjson.version>
		<zkclient.version>0.4</zkclient.version>
		<log4j.version>2.15.0</log4j.version>
		<rocketmq.client.version>4.9.0</rocketmq.client.version>
		<lmax.version>3.4.2</lmax.version>
		<ojdbc6.version>11.2.0.2.0</ojdbc6.version>
		<hessian.version>4.0.7</hessian.version>
		<atomikos.version>3.9.3</atomikos.version>
		<commons.pool.version>1.6</commons.pool.version>
		<commons.beanutils.version>1.9.2</commons.beanutils.version>
		<com.howbuy.howbuy-boot-actuator.version>1.1.6-RELEASE</com.howbuy.howbuy-boot-actuator.version>

		<com.howbuy.message-2.version>2.1.3-RELEASE</com.howbuy.message-2.version>
		<com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
		<com.howbuy.howbuy-message-service.version>2.3.0-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.howbuy-message-amq.version>2.3.0-RELEASE</com.howbuy.howbuy-message-amq.version>
		<com.howbuy.howbuy-message-rocket.version>2.3.0-RELEASE</com.howbuy.howbuy-message-rocket.version>
		<com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>

		<com.howbuy.robot-order-center-client.version>4.7.99-RELEASE</com.howbuy.robot-order-center-client.version>
		<com.howbuy.robot-order-center.version>4.7.99-RELEASE</com.howbuy.robot-order-center.version>
		<com.howbuy.batch-center.version>4.7.99-RELEASE</com.howbuy.batch-center.version>
		<com.howbuy.batch-center-client.version>4.7.99-RELEASE</com.howbuy.batch-center-client.version>
		<com.howbuy.order-center-client.version>4.7.99-RELEASE</com.howbuy.order-center-client.version>
		<com.howbuy.order-center.version>4.7.99-RELEASE</com.howbuy.order-center.version>

		<com.howbuy.message-center-client.version>4.7.34-RELEASE</com.howbuy.message-center-client.version>
		<com.howbuy.howbuy-fund-client.version>release-20241204-wechat-RELEASE</com.howbuy.howbuy-fund-client.version>

		<com.howbuy.tms-common-service.version>4.7.64-RELEASE</com.howbuy.tms-common-service.version>
		<com.howbuy.tms-common-client.version>4.7.64-RELEASE</com.howbuy.tms-common-client.version>
		<com.howbuy.tms-common-lang.version>4.7.64-RELEASE</com.howbuy.tms-common-lang.version>
		<com.howbuy.tms-common-outerservice.version>4.7.64-RELEASE</com.howbuy.tms-common-outerservice.version>
		<com.howbuy.tms-common-validator.version>4.7.64-RELEASE</com.howbuy.tms-common-validator.version>
		<com.howbuy.tms-common-enums.version>4.7.64-RELEASE</com.howbuy.tms-common-enums.version>
		<com.howbuy.tms-common-message-service.version>4.7.64-RELEASE</com.howbuy.tms-common-message-service.version>

		<com.howbuy.product-center-model.version>4.7.64-RELEASE</com.howbuy.product-center-model.version>
		<com.howbuy.product-center-client.version>4.7.64-RELEASE</com.howbuy.product-center-client.version>

		<com.howbuy.howbuy-message-service.version>2.2.3.1-RELEASE</com.howbuy.howbuy-message-service.version>
		<com.howbuy.howbuy-message-amq-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-amq-2.version>
		<com.howbuy.howbuy-message-rocket-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-rocket-2.version>

	<com.howbuy.order-center-interaction.version>4.7.99-RELEASE</com.howbuy.order-center-interaction.version>
</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-validation</artifactId>
				<version>${spring-boot.version}</version>
				<scope>compile</scope>
				<exclusions>
					<exclusion>
						<artifactId>tomcat-embed-el</artifactId>
						<groupId>org.apache.tomcat.embed</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo</artifactId>
				<version>${dubbo.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>servlet-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<groupId>log4j</groupId>
						<artifactId>log4j</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-spring-boot-starter</artifactId>
				<version>${dubbo.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.zookeeper</groupId>
				<artifactId>zookeeper</artifactId>
				<version>${zookeeper.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.dubbo</groupId>
				<artifactId>dubbo-dependencies-zookeeper</artifactId>
				<version>${dubbo.version}</version>
				<type>pom</type>
				<exclusions>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-spring-boot-starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
				<version>2.2.0.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
				<version>2.2.0.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>druid</artifactId>
				<version>${druid.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.pagehelper</groupId>
				<artifactId>pagehelper</artifactId>
				<version>${pagehelper.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-service</artifactId>
				<version>${com.howbuy.howbuy-message-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-amq</artifactId>
				<version>${com.howbuy.howbuy-message-amq.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-message-rocket</artifactId>
				<version>${com.howbuy.howbuy-message-rocket.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-ccms-watcher</artifactId>
				<version>${com.howbuy.howbuy-ccms-watcher.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>utils</artifactId>
				<version>1.0.0-release</version>
				<exclusions>
					<exclusion>
						<artifactId>javassist</artifactId>
						<groupId>org.javassist</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			
			<dependency>
				<artifactId>howbuy-cachemanagement</artifactId>
				<groupId>com.howbuy</groupId>
				<version>${com.howbuy.howbuy-cachemanagement.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>javassist</artifactId>
						<groupId>org.javassist</groupId>
					</exclusion>
					<exclusion>
						<artifactId>zkutils</artifactId>
						<groupId>com.howbuy</groupId>
					</exclusion>
					<exclusion>
						<artifactId>howbuy-ccms-independent</artifactId>
						<groupId>com.howbuy</groupId>
					</exclusion>
					<exclusion>
						<artifactId>fastjson</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>activemq-all</artifactId>
						<groupId>org.apache.activemq</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.2</version>
			</dependency>
			
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>2.9.3</version>
			</dependency>
			<dependency>
				<groupId>org.apache.rocketmq</groupId>
				<artifactId>rocketmq-client</artifactId>
				<version>${rocketmq.client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>atomikos-util</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			<dependency>
				<groupId>com.atomikos</groupId>
				<artifactId>transactions-jdbc</artifactId>
				<version>${atomikos.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>robot-order-center-client</artifactId>
				<version>${com.howbuy.robot-order-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>robot-order-center-service</artifactId>
				<version>${com.howbuy.robot-order-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>robot-order-center-dao</artifactId>
				<version>${com.howbuy.robot-order-center.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>order-center-client</artifactId>
				<version>${com.howbuy.order-center-interaction.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-service</artifactId>
				<version>${com.howbuy.tms-common-service.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>springframework-addons</artifactId>
						<groupId>net.unicon.springframework</groupId>
					</exclusion>
					<exclusion>
						<artifactId>servlet-api</artifactId>
						<groupId>javax.servlet</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-client</artifactId>
				<version>${com.howbuy.tms-common-client.version}</version>
			</dependency>

			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-lang</artifactId>
				<version>${com.howbuy.tms-common-lang.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-outerservice</artifactId>
				<version>${com.howbuy.tms-common-outerservice.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>dubbo</artifactId>
						<groupId>com.alibaba</groupId>
					</exclusion>
					<exclusion>
						<artifactId>springframework-addons</artifactId>
						<groupId>net.unicon.springframework</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-validator</artifactId>
				<version>${com.howbuy.tms-common-validator.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-enums</artifactId>
				<version>${com.howbuy.tms-common-enums.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-message-service</artifactId>
				<version>${com.howbuy.tms-common-message-service.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>message-center-client</artifactId>
				<version>${com.howbuy.message-center-client.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>batch-center-client</artifactId>
				<version>${com.howbuy.batch-center.version}</version>
			</dependency>
			<dependency>
				<groupId>com.caucho</groupId>
				<artifactId>hessian</artifactId>
				<version>${hessian.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.oracle</groupId>
				<artifactId>ojdbc6</artifactId>
				<version>${ojdbc6.version}</version>
			</dependency>
			<dependency>
				<groupId>com.101tec</groupId>
				<artifactId>zkclient</artifactId>
				<version>${zkclient.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>commons-pool</groupId>
				<artifactId>commons-pool</artifactId>
				<version>${commons.pool.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-beanutils</groupId>
				<artifactId>commons-beanutils</artifactId>
				<version>${commons.beanutils.version}</version>
			</dependency>
			
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuy-fund-client</artifactId>
				<version>${com.howbuy.howbuy-fund-client.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-log4j12</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<artifactId>log4j</artifactId>
						<groupId>log4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy.tms</groupId>
				<artifactId>tms-common-log-pattern</artifactId>
				<version>1.0.0-RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-model</artifactId>
				<version>${com.howbuy.product-center-model.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.interlayer</groupId>
				<artifactId>product-center-client</artifactId>
				<version>${com.howbuy.product-center-client.version}</version>
			</dependency>
			<dependency>
				<groupId>joda-time</groupId>
				<artifactId>joda-time</artifactId>
				<version>2.10.6</version>
			</dependency>
			<dependency>
				<groupId>com.lmax</groupId>
				<artifactId>disruptor</artifactId>
				<version>${lmax.version}</version>
			</dependency>
			<dependency>
				<groupId>com.howbuy.boot</groupId>
				<artifactId>howbuy-boot-actuator</artifactId>
				<version>${com.howbuy.howbuy-boot-actuator.version}</version>
				<exclusions>
					<exclusion>
						<artifactId>*</artifactId>
						<groupId>ch.qos.logback</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.howbuy</groupId>
				<artifactId>howbuyUtil</artifactId>
				<version>1.0.0</version>
			</dependency>
			<dependency>
				<groupId>net.sourceforge.pinyin4j</groupId>
				<artifactId>pinyin4j</artifactId>
				<version>2.5.0</version>
			</dependency>
			<dependency>
				<groupId>org.softamis</groupId>
				<artifactId>cluster4spring</artifactId>
				<version>0.85</version>
			</dependency>
			
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>20.0</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<attach>true</attach>
				</configuration>
				<executions>
					<execution>
						<phase>compile</phase>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
				<configuration>
					<skipTests>true</skipTests>
					<includes>
						<include>**/*TestM.java</include>
					</includes>
					<systemPropertyVariables>
						<jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<configuration>
					<archive>
						<manifestEntries>
							<Package-Stamp>${parelease}</Package-Stamp>
						</manifestEntries>
					</archive>
				</configuration>
			</plugin>
		</plugins>
	</build>
	</project>