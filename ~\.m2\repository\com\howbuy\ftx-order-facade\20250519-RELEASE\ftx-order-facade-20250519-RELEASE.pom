<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ftx-order</artifactId>
        <groupId>com.howbuy</groupId>
        <version>20250519-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ftx-order-facade</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.howbuy.common</groupId>
            <artifactId>common-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy.common</groupId>
            <artifactId>common-service</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>ftx-common-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>