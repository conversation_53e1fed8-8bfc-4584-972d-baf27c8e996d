<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-ehowbuy-common-parent</artifactId>
		<version>20250515-001-RELEASE</version>
	</parent>

	<name>cgi-common</name>
	<artifactId>cgi-common</artifactId>
	<version>20250515-001-RELEASE</version>

	<dependencies>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
		    <artifactId>tms-common-log-pattern</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-interceptor</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cms-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.common</groupId>
			<artifactId>common-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>utils</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.extend</groupId>
			<artifactId>Howbuy-security</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.extend</groupId>
			<artifactId>HowbuyValidator</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>howbuy-cachemanagement</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
		</dependency>
		<dependency>
			<groupId>net.sf.oval</groupId>
			<artifactId>oval</artifactId>
		</dependency>
		<!-- spring framework -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
		</dependency>
		<dependency>
			<groupId>com.danga</groupId>
			<artifactId>Memcached-Java-Client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
		</dependency>
		<!-- jackson2 -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>
		<!-- jsonlib -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<classifier>jdk15</classifier>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.16</version>
			<scope>provided</scope>
		</dependency>

	</dependencies>

</project>