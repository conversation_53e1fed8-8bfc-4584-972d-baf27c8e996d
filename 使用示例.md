# 系统异常监控 Filter 使用示例

## 场景描述

当系统发生空指针异常等系统级异常时，全局异常处理器会将异常转换为统一的错误响应格式：

```json
{
  "code": "1999",
  "body": null,
  "desc": "系统错误，请联系好买",
  "timestampServer": "1667989593758"
}
```

原有的拦截器无法捕获这种被处理过的异常，因此需要通过 Filter 来监控响应体中的错误码。

## 解决方案部署

### 1. 确认文件已创建

确保以下文件已存在：
- `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/filter/SystemExceptionMonitorFilter.java`
- `cgi-ehowbuy/cgi-simu/src/main/java/com/howbuy/cgi/trade/simu/common/config/FilterConfig.java` (已修改)

### 2. 验证配置

检查 `FilterConfig.java` 中是否包含以下配置：

```java
@Bean
public FilterRegistrationBean<SystemExceptionMonitorFilter> systemExceptionMonitorFilter() {
    FilterRegistrationBean<SystemExceptionMonitorFilter> registration = new FilterRegistrationBean<>();
    registration.setFilter(new SystemExceptionMonitorFilter());
    registration.addUrlPatterns("/simu/*");
    registration.setName("systemExceptionMonitorFilter");
    registration.setOrder(2); // 设置在字符编码过滤器之后执行
    return registration;
}
```

### 3. 启动应用

重启应用后，过滤器会自动生效。

## 监控效果

### 触发系统异常

访问会产生系统异常的接口，例如：
```
GET /cgi/simu/test/nullpointer.htm
```

### 预期响应

```json
{
  "code": "1999",
  "body": null,
  "desc": "系统错误，请联系好买",
  "timestampServer": "1667989593758"
}
```

### 告警信息

系统会自动发送告警到 `OpsSysMonitor`，告警内容示例：

```json
{
  "alertType": "SYSTEM_EXCEPTION",
  "requestUri": "/cgi/simu/test/nullpointer.htm",
  "requestMethod": "GET",
  "traceId": "7f349b92d8884254aefaa36e35e1bd24",
  "remoteAddr": "127.0.0.1",
  "responseCode": "1999",
  "responseDesc": "系统错误，请联系好买",
  "detectionMethod": "RESPONSE_BODY_CHECK",
  "requestParams": {
    "testParam": "testValue"
  },
  "responseBodyLength": 101,
  "responseBody": "{\"code\":\"1999\",\"body\":null,\"desc\":\"系统错误，请联系好买\",\"timestampServer\":\"1667989593758\"}"
}
```

## 日志输出

在应用日志中会看到类似以下内容：

```
2025-01-22 10:30:15.123 ERROR [http-nio-8080-exec-6] c.h.c.t.s.f.SystemExceptionMonitorFilter - 系统异常告警已发送(通过响应体检测): {"alertType":"SYSTEM_EXCEPTION",...}
```

## 配置说明

### 监控路径
默认监控 `/simu/*` 路径下的所有请求，可以根据需要修改：

```java
registration.addUrlPatterns("/simu/*", "/other/*");
```

### 系统错误码
默认监控错误码 `"1999"`，可以在 `SystemExceptionMonitorFilter` 中修改：

```java
private static final String SYSTEM_ERROR_CODE = "1999";
```

### 过滤器顺序
默认设置为 `order=2`，确保在字符编码过滤器之后执行。可以根据需要调整：

```java
registration.setOrder(1); // 更高优先级
```

## 性能考虑

1. **响应体包装**: 过滤器会包装响应以捕获内容，对性能有轻微影响
2. **JSON解析**: 只在检测到可能的JSON响应时才进行解析
3. **告警发送**: 异步发送告警，不阻塞主请求流程

## 故障排除

### 1. 过滤器未生效
- 检查 `FilterConfig` 中的配置是否正确
- 确认应用已重启
- 检查请求路径是否匹配 `/simu/*`

### 2. 告警未发送
- 检查 `OpsSysMonitor` 是否可用
- 查看应用日志中是否有异常信息
- 确认响应体格式为JSON且包含 `code: "1999"`

### 3. 响应内容异常
- 检查 `ResponseWrapper` 是否正确包装响应
- 确认响应内容被正确写回原始响应

## 扩展功能

### 1. 监控其他错误码
可以扩展监控多个错误码：

```java
private static final Set<String> SYSTEM_ERROR_CODES = Set.of("1999", "5000", "5001");

if (jsonResponse != null && SYSTEM_ERROR_CODES.contains(jsonResponse.getString("code"))) {
    // 发送告警
}
```

### 2. 自定义告警格式
可以在 `buildAlertInfo` 方法中自定义告警信息格式。

### 3. 添加更多监控指标
可以添加响应时间、请求频率等监控指标。

## 总结

通过 Filter + Interceptor 双重监控机制，可以全面覆盖系统异常的监控需求：
- **Filter**: 监控被全局异常处理器处理的异常（通过响应体错误码）
- **Interceptor**: 监控直接抛出的异常

这种方案既保持了原有功能的完整性，又解决了异常被处理后无法监控的问题。
