package com.howbuy.cgi.trade.simu.controller.test;

import com.howbuy.cgi.common.CGIConstants;
import com.howbuy.cgi.common.enums.BizErrorEnum;
import com.howbuy.cgi.common.exception.BizException;
import com.howbuy.cgi.trade.simu.controller.AbstractSimuCGIController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 异常测试控制器
 * 用于测试系统异常监控拦截器的功能
 * 注意：此控制器仅用于测试目的，生产环境中应该移除
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-21
 */
@Controller
public class ExceptionTestController extends AbstractSimuCGIController {

    /**
     * 测试空指针异常
     * 访问路径：/simu/test/nullpointer.htm
     */
    @RequestMapping("/simu/test/nullpointer.htm")
    public void testNullPointerException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试空指针异常");
        
        // 故意制造空指针异常
        String nullString = null;
        int length = nullString.length(); // 这里会抛出NullPointerException
        
        Map<String, Object> result = new HashMap<>();
        result.put("length", length);
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 测试数组越界异常
     * 访问路径：/simu/test/arrayindex.htm
     */
    @RequestMapping("/simu/test/arrayindex.htm")
    public void testArrayIndexOutOfBoundsException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试数组越界异常");
        
        // 故意制造数组越界异常
        int[] array = {1, 2, 3};
        int value = array[10]; // 这里会抛出ArrayIndexOutOfBoundsException
        
        Map<String, Object> result = new HashMap<>();
        result.put("value", value);
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 测试类型转换异常
     * 访问路径：/simu/test/classcast.htm
     */
    @RequestMapping("/simu/test/classcast.htm")
    public void testClassCastException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试类型转换异常");
        
        // 故意制造类型转换异常
        Object obj = "这是一个字符串";
        Integer number = (Integer) obj; // 这里会抛出ClassCastException
        
        Map<String, Object> result = new HashMap<>();
        result.put("number", number);
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 测试数字格式异常
     * 访问路径：/simu/test/numberformat.htm
     */
    @RequestMapping("/simu/test/numberformat.htm")
    public void testNumberFormatException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试数字格式异常");
        
        // 故意制造数字格式异常
        String invalidNumber = "abc123";
        int number = Integer.parseInt(invalidNumber); // 这里会抛出NumberFormatException
        
        Map<String, Object> result = new HashMap<>();
        result.put("number", number);
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 测试业务异常（不应该被系统异常监控拦截器处理）
     * 访问路径：/simu/test/bizexception.htm
     */
    @RequestMapping("/simu/test/bizexception.htm")
    public void testBizException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试业务异常");
        
        // 抛出业务异常，这个不应该被系统异常监控拦截器处理
        throw new BizException(BizErrorEnum.NEED_RELOGIN.getCode(), "这是一个测试业务异常");
    }

    /**
     * 测试嵌套业务异常（包装在RuntimeException中的BizException）
     * 访问路径：/simu/test/nestedbizexception.htm
     */
    @RequestMapping("/simu/test/nestedbizexception.htm")
    public void testNestedBizException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试嵌套业务异常");
        
        try {
            // 先抛出业务异常
            throw new BizException(BizErrorEnum.SYSTEM_ERROR.getCode(), "内部业务异常");
        } catch (BizException e) {
            // 包装成RuntimeException再抛出，这个也不应该被系统异常监控拦截器处理
            throw new RuntimeException("包装的业务异常", e);
        }
    }

    /**
     * 测试正常请求（不抛出任何异常）
     * 访问路径：/simu/test/normal.htm
     */
    @RequestMapping("/simu/test/normal.htm")
    public void testNormalRequest(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试正常请求");
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "success");
        result.put("message", "正常请求，没有异常");
        result.put("timestamp", System.currentTimeMillis());
        
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }

    /**
     * 测试带敏感参数的异常请求
     * 访问路径：/simu/test/sensitiveparams.htm
     * 参数示例：?password=123456&mobile=13800138000&normalParam=test
     */
    @RequestMapping("/simu/test/sensitiveparams.htm")
    public void testSensitiveParamsException(HttpServletRequest request, HttpServletResponse response) throws Exception {
        log.info("测试带敏感参数的异常请求");
        
        // 获取参数
        String password = request.getParameter("password");
        String mobile = request.getParameter("mobile");
        String normalParam = request.getParameter("normalParam");
        
        log.info("接收到参数: password={}, mobile={}, normalParam={}", 
                password != null ? "***" : null, 
                mobile != null ? "***" : null, 
                normalParam);
        
        // 故意制造异常，测试敏感参数是否被正确过滤
        String nullString = null;
        int length = nullString.length(); // 抛出NullPointerException
        
        Map<String, Object> result = new HashMap<>();
        result.put("length", length);
        write(result, CGIConstants.RESPONSE_CONTENT_TYPE_CIPHERTEXT, response);
    }
}
