<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.howbuy.cgi</groupId>
		<artifactId>cgi-service</artifactId>
		<version>20250711-001-RELEASE</version>
	</parent>

	<name>service-common</name>
	<artifactId>service-common</artifactId>
	<version>20250711-001-RELEASE</version>

	<dependencies>

		<dependency>
		    <groupId>com.howbuy.dfile</groupId>
		    <artifactId>howbuy-dfile-service</artifactId>
		</dependency>

		<dependency>
		    <groupId>com.howbuy.dfile</groupId>
		    <artifactId>howbuy-dfile-impl-nfs</artifactId>
		</dependency>

		<dependency>
		    <groupId>com.howbuy.dfile</groupId>
		    <artifactId>howbuy-dfile-impl-webdav</artifactId>
		</dependency>

		<dependency>
		    <groupId>eu.bitwalker</groupId>
			<artifactId>UserAgentUtils</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>HowbuyServiceBus</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy</groupId>
			<artifactId>HowbuyServiceCommon</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
		    <artifactId>tms-common-log-pattern</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
	    	<artifactId>product-center-model</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.interlayer</groupId>
	    	<artifactId>product-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-message-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>robot-order-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>elasticsearch-center-client</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.cgi</groupId>
			<artifactId>cgi-common</artifactId>
		</dependency>

		<!-- howbuy-tms -->
		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
		</dependency>

		<!-- howbuy-tp -->
		<dependency>
			<groupId>com.howbuy.common</groupId>
			<artifactId>common-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.payonline</groupId>
			<artifactId>pay-online-facade</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.cc</groupId>
			<artifactId>center-client</artifactId>
		</dependency>

		<!-- spring framework -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>

		<!-- javax -->
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>pension-order-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-context</artifactId>
		</dependency>

	</dependencies>

</project>