<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.howbuy.tms</groupId>
		<artifactId>robot-order-center</artifactId>
		<version>4.7.99-RELEASE</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<name>robot-order-center-client</name>
	<artifactId>robot-order-center-client</artifactId>
	<version>4.7.99-RELEASE</version>
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.encoding>UTF-8</maven.compiler.encoding>
		<java.version>1.8</java.version>
		<tms.version>3.4.69-release</tms.version>
		<lombok.version>1.18.20</lombok.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-enums</artifactId>
			<version>${com.howbuy.tms-common-enums.version}</version>
		</dependency>

		<dependency>
			<groupId>com.howbuy.tms</groupId>
			<artifactId>tms-common-client</artifactId>
			<version>${com.howbuy.tms-common-client.version}</version>
		</dependency>

		<!-- 第三方工具依赖 -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		
	</dependencies>
	
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.4</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.5</version>
			</plugin>
		</plugins>
	</build>
	<distributionManagement>
		<repository>
			<id>howbuy-releases</id>
			<name>howbuy-releases</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
		</repository>
		<snapshotRepository>
			<id>howbuy-snapshots</id>
			<name>howbuy-snapshots</name>
			<url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
		</snapshotRepository>
	</distributionManagement>
</project>