<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.howbuy</groupId>
  <artifactId>howbuy-session</artifactId>
  <version>1.0.0-release</version>
  <packaging>jar</packaging>

  <name>howbuy-session</name>
  <url>http://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>



  <dependencyManagement>
      <dependencies>
          <dependency>
              <groupId>com.howbuy</groupId>
              <artifactId>howbuy-infrastructure</artifactId>
              <version>release-SNAPSHOT</version>
              <type>pom</type>
              <scope>import</scope>
          </dependency>
      </dependencies>
  </dependencyManagement>




  <dependencies>
  	  
  	  <dependency>
  	  	<groupId>com.howbuy</groupId>
		<artifactId>howbuy-cachemanagement</artifactId>
		<!-- <exclusions>
			<exclusion>
				<artifactId>servlet-api</artifactId>
				<groupId>javax.servlet</groupId>
			</exclusion>
		</exclusions> -->
  	  </dependency>
	  
  	  <dependency>
	  	  <groupId>org.springframework</groupId>
		  <artifactId>spring-web</artifactId>
	  </dependency>
  	  
  	  <!-- slf4j -->
      <dependency>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-api</artifactId>
          <version>1.7.7</version>
      </dependency>
      <dependency>
          <groupId>org.slf4j</groupId>
          <artifactId>slf4j-log4j12</artifactId>
          <version>1.7.7</version>
      </dependency>
      <dependency>
          <groupId>log4j</groupId>
          <artifactId>log4j</artifactId>
          <version>1.2.17</version>
      </dependency>

      <!-- <dependency>
		  <groupId>javax.servlet</groupId>
		  <artifactId>javax.servlet-api</artifactId>
		  <version>3.0.1</version>
		  <scope>provided</scope>
	  </dependency> -->
	  
      <!-- junit -->
	  <dependency>
	      <groupId>junit</groupId>
	      <artifactId>junit</artifactId>
	      <version>4.12</version>
	      <scope>test</scope>
	  </dependency>
  </dependencies>
  
  
   <build>
        <sourceDirectory>${basedir}/src/main/java/</sourceDirectory>
        <resources>
            <resource>
                <directory>${basedir}/src/main/resources/</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.9</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.1</version>
            </plugin>

            <!-- 打包源码 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>howbuy-releases</id>
            <name>howbuy-releases</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>howbuy-snapshots</id>
            <name>howbuy-snapshots</name>
            <url>http://mvn.intelnal.howbuy.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
